import requests
from datetime import datetime, timezone, timedelta
import json
from typing import List, Dict, Tuple
import os
from dotenv import load_dotenv
import logging
from logging.handlers import RotatingFileHandler
import time  # 添加 time 模块导入
import sys  # 添加sys模块导入
import argparse
import re  # 用于正则表达式匹配
import signal  # 添加信号处理
from pathlib import Path  # 添加路径处理
from directus_api_client import DirectusClient

# 在导入后立即加载环境变量，确保所有代码都能访问
load_dotenv()

class FengZSCollector:
    def __init__(self, verbose_logging=False, channel_interval=None, page_interval=None, auto_login=True, test_login_only=False, login_retry_count=3):
        # 设置日志
        self.setup_logging(verbose_logging)
        
        self.verbose_logging = verbose_logging  # 是否记录详细日志
        self.test_login_only = test_login_only  # 是否仅测试登录功能
        self.login_retry_count = login_retry_count  # 登录重试次数
        
        # webhook配置
        self.webhook_url = os.getenv('WECOM_WEBHOOK_URL')

        # API配置
        self.directus_token = os.getenv('DIRECTUS_TOKEN')
        self.directus_api_url = os.getenv('DIRECTUS_API_URL')
        self.fengzs_api_url = os.getenv('FENGZS_API_URL')
        self.bss_url = os.getenv('BSS_URL', 'https://bss.phone580.com')  # BSS基础URL
        self.hive_url = os.getenv('HIVE_URL', 'https://hive.phone580.com')  # HIVE基础URL

        # 初始化Directus客户端
        self.directus_client = DirectusClient(
            api_url=self.directus_api_url,
            token=self.directus_token,
            logger=self.logger
        )
        
        # Agent ID配置，用于SSO跳转
        self.agent_id = os.getenv('AGENT_ID', '22032518oQft')  # 默认值
        self.logger.info(f"使用agent_id: {self.agent_id}")
        
        # 配置登录参数
        self.login_username = os.getenv('LOGIN_USERNAME')
        self.login_password = os.getenv('LOGIN_PASSWORD')
        
        # 配置请求间隔（从命令行参数获取或环境变量）
        self.channel_interval = float(os.getenv('CHANNEL_INTERVAL')) if channel_interval is None else channel_interval
        self.page_interval = float(os.getenv('PAGE_INTERVAL')) if page_interval is None else page_interval
        
        # 加载hivecookie.json文件
        self.hive_cookie = self.load_hive_cookies()
        
        # 获取凤之树cookie
        self.fengzs_cookie = ''
        
        # 记录登录状态
        self.login_successful = False
        
        # 如果需要自动登录，执行登录步骤（带重试）
        if auto_login:
            self.logger.info("开始执行自动登录流程")
            self.login_successful = self.login_with_retry()
            if not self.login_successful:
                error_msg = f"自动登录失败，已尝试 {self.login_retry_count} 次"
                self.logger.error(error_msg)
                print(f"\n错误：{error_msg}\n")
                
                # 如果是测试登录模式，则直接退出
                if self.test_login_only:
                    print("\n❌ 登录测试失败！\n")
                    sys.exit(1)
                
                # 不再尝试加载cookie.json，直接退出
                print("\n❌ 自动登录失败，程序退出\n")
                sys.exit(1)
            else:
                self.logger.info("自动登录成功，已获取并保存cookie")
                # 更新当前的cookie，确保使用最新的
                self.fengzs_cookie = self.load_cookies()
                
                if self.test_login_only:
                    print("\n✅ 登录测试成功！已获取并保存cookie\n")
                    sys.exit(0)  # 登录测试成功，直接退出
        else:
            # 禁用自动登录，但仍然要求之前已经登录成功过
            if not os.path.exists(os.path.join(os.path.dirname(__file__), 'cookie.json')):
                error_msg = "已禁用自动登录，但未找到cookie.json文件"
                self.logger.error(error_msg)
                print(f"\n错误：{error_msg}\n")
                sys.exit(1)  # 退出程序
                
            # 加载现有cookie
            self.fengzs_cookie = self.load_cookies()
            if not self.fengzs_cookie:
                error_msg = "已禁用自动登录，且cookie.json文件无效"
                self.logger.error(error_msg)
                print(f"\n错误：{error_msg}\n")
                sys.exit(1)  # 退出程序
                
            # 设置登录状态为成功（因为用户选择跳过登录）
            self.login_successful = True
            
        # 修改日期配置，使用时间区间
        beijing_tz = timezone(timedelta(hours=8))
        now = datetime.now(beijing_tz)  # 直接使用北京时区创建时间
        
        # 获取昨天的日期
        yesterday = now - timedelta(days=1)
        default_begin = yesterday.strftime('%Y-%m-%d 00:00:00')
        default_end = yesterday.strftime('%Y-%m-%d 23:59:59')
        
        # 如果环境变量中设置了固定时间，则使用固定时间，否则使用昨天的时间
        self.original_begin_date = os.getenv('BEGIN_DATE', default_begin)
        self.original_end_date = os.getenv('END_DATE', default_end)
        self.logger.info(f"原始采集时间区间设置为: {self.original_begin_date} 至 {self.original_end_date}")
        
        # 当前使用的时间区间，将在run方法中按需设置
        self.begin_date = self.original_begin_date
        self.end_date = self.original_end_date
        
        self.logger.info(f"渠道请求间隔设置为: {self.channel_interval}秒")
        self.logger.info(f"分页请求间隔设置为: {self.page_interval}秒")

        # 停止信号相关
        self.stop_signal_file = Path(__file__).parent / '.stop_collector'
        self.should_stop = False

        # 设置信号处理器
        signal.signal(signal.SIGTERM, self._signal_handler)
        signal.signal(signal.SIGINT, self._signal_handler)

        self.logger.info("FengZSCollector 初始化完成")

    def _signal_handler(self, signum, frame):
        """信号处理器"""
        if not self.should_stop:  # 只在第一次收到信号时记录日志
            self.logger.info(f"收到停止信号 {signum}，准备优雅退出...")
            self.should_stop = True

    def check_stop_signal(self) -> bool:
        """检查是否应该停止"""
        if self.should_stop:
            return True

        # 检查停止信号文件
        if self.stop_signal_file.exists():
            self.logger.info("检测到停止信号文件，准备停止...")
            self.should_stop = True
            return True

        return False

    def load_hive_cookies(self) -> str:
        """从hivecookie.json文件加载cookie"""
        try:
            hive_cookie_path = os.path.join(os.path.dirname(__file__), 'hivecookie.json')
            
            # 检查hivecookie.json文件是否存在
            if not os.path.exists(hive_cookie_path):
                self.logger.error("未找到hivecookie.json文件")
                return ''
            
            # 加载cookie文件
            with open(hive_cookie_path, 'r', encoding='utf-8') as f:
                cookies = json.load(f)
            
            # 检查cookie是否为空列表或格式不正确
            if not cookies or not isinstance(cookies, list):
                self.logger.error("hivecookie.json文件格式不正确或为空")
                return ''
            
            # 生成cookie字符串
            cookie_str = '; '.join([f"{cookie['name']}={cookie['value']}" for cookie in cookies])
            
            self.logger.info("成功加载hive cookie")
            return cookie_str
            
        except Exception as e:
            self.logger.error(f"加载hive cookie失败: {str(e)}")
            return ''

    def perform_login(self) -> bool:
        """执行登录流程，获取和保存cookie"""
        try:
            # 首先访问主页
            self.logger.info("访问hive主页")
            hive_headers = {
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7',
                'Connection': 'keep-alive',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36',
                'Cookie': self.hive_cookie,
                'Upgrade-Insecure-Requests': '1',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'none',
                'Sec-Fetch-User': '?1'
            }
            
            session = requests.Session()
            
            # 添加所有cookie到会话
            if self.hive_cookie:
                # 解析hivecookie字符串并添加到会话
                self.logger.info("添加hivecookie到会话")
                hivecookie_path = os.path.join(os.path.dirname(__file__), 'hivecookie.json')
                if os.path.exists(hivecookie_path):
                    try:
                        with open(hivecookie_path, 'r', encoding='utf-8') as f:
                            cookies = json.load(f)
                            
                        if isinstance(cookies, list) and len(cookies) > 0:
                            cookie_count = 0
                            # 添加cookie到会话
                            for cookie in cookies:
                                try:
                                    domain = cookie.get('domain', '')
                                    if 'phone580.com' in domain:
                                        cookie_dict = {
                                            'name': cookie.get('name'),
                                            'value': cookie.get('value'),
                                            'domain': cookie.get('domain'),
                                            'path': cookie.get('path', '/')
                                        }
                                        session.cookies.set(cookie_dict['name'], cookie_dict['value'], domain=cookie_dict['domain'], path=cookie_dict['path'])
                                        cookie_count += 1
                                except Exception as cookie_err:
                                    self.logger.warning(f"添加cookie失败: {str(cookie_err)}")
                            
                            self.logger.info(f"成功添加 {cookie_count} 个cookie到会话")
                    except Exception as e:
                        self.logger.error(f"加载hivecookie.json失败: {str(e)}")
            
            # 访问主页
            main_resp = session.get(
                f"{self.hive_url}/main.html", 
                headers=hive_headers
            )
            
            if main_resp.status_code != 200:
                self.logger.error(f"访问主页失败，状态码: {main_resp.status_code}")
                return False
                
            # 增加请求getKey接口
            self.logger.info("请求getKey接口")
            get_key_resp = session.get(
                f"{self.hive_url}/login/getKey",
                headers={
                    **hive_headers,
                    'Referer': f"{self.hive_url}/index.html"
                }
            )
            
            if get_key_resp.status_code != 200:
                self.logger.error(f"请求getKey接口失败，状态码: {get_key_resp.status_code}")
                # 不直接返回失败，尝试继续登录流程
                self.logger.warning("尝试继续登录流程")
            else:
                self.logger.info(f"getKey接口响应: {get_key_resp.text[:100]}")
                
            self.logger.info("访问login接口")
            # 访问登录接口
            login_headers = {
                'Accept': '*/*',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7',
                'Connection': 'keep-alive',
                'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
                'Origin': 'https://hive.phone580.com',
                'Referer': 'https://hive.phone580.com/index.html',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36',
                'X-Requested-With': 'XMLHttpRequest',
                'sec-ch-ua': '"Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"'
            }
            
            login_data = {
                'password': self.login_password,
                'userName': self.login_username,
                'authCode': '',
                'appId': '',
                'redirectUrl': '',
                'loginCode': ''
            }
            
            login_resp = session.post(
                f"{self.hive_url}/login", 
                headers=login_headers,
                data=login_data
            )
            
            if login_resp.status_code != 200:
                self.logger.error(f"登录请求失败，状态码: {login_resp.status_code}")
                return False
            
            # 记录完整响应内容便于调试
            self.logger.info(f"登录接口响应: {login_resp.text}")
            
            try:
                login_json = login_resp.json()
                # 修改登录成功判断逻辑，针对{'code': 1, 'message': '操作成功'}这种情况
                if login_json.get('success') is True or (login_json.get('code') == 1 and '成功' in login_json.get('message', '')):
                    self.logger.info("登录成功")
                else:
                    self.logger.error(f"登录失败，响应: {login_json}")
                    return False
            except Exception as e:
                self.logger.error(f"解析登录响应失败: {str(e)}")
                return False
            
            # 访问agents页面，这会触发跳转到BSS
            self.logger.info("访问agents页面，准备获取BSS cookie")
            agent_url = f"{self.hive_url}/agents/{self.agent_id}"
            
            # 改进的请求头
            agent_headers = {
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7',
                'Connection': 'keep-alive',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36',
                'Referer': f"{self.hive_url}/main.html",
                'Upgrade-Insecure-Requests': '1',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'same-origin',
                'Sec-Fetch-User': '?1'
            }
            
            # 允许重定向
            agent_resp = session.get(
                agent_url, 
                headers=agent_headers,
                allow_redirects=True
            )
            
            # 记录重定向过程中的所有URL
            self.logger.info(f"重定向历史: {[r.url for r in agent_resp.history]}")
            self.logger.info(f"最终URL: {agent_resp.url}")
            
            # 尝试重新处理agents请求
            if "bss.phone580.com" not in agent_resp.url:
                self.logger.warning("第一次尝试跳转未成功，尝试直接访问BSS系统")
                
                # 直接尝试访问BSS系统
                bss_resp = session.get(
                    f"{self.bss_url}/",
                    headers={
                        **agent_headers,
                        'Referer': self.hive_url
                    },
                    allow_redirects=True
                )
                
                self.logger.info(f"BSS跳转历史: {[r.url for r in bss_resp.history]}")
                self.logger.info(f"BSS最终URL: {bss_resp.url}")
                
                # 还不成功的话,尝试第三种方法
                if "bss.phone580.com" not in bss_resp.url:
                    self.logger.warning("第二次尝试未成功，尝试fzs-microbee-admin接口")
                    
                    # 直接尝试访问凤之树API接口获取cookie
                    fzs_resp = session.get(
                        f"{self.fengzs_api_url}/",
                        headers={
                            **agent_headers,
                            'Referer': self.hive_url
                        },
                        allow_redirects=True
                    )
                    
                    self.logger.info(f"FZS API跳转历史: {[r.url for r in fzs_resp.history]}")
                    self.logger.info(f"FZS API最终URL: {fzs_resp.url}")
                    
                    # 如果fzs也不成功，那就是真的失败了
                    if "bss.phone580.com" not in fzs_resp.url:
                        self.logger.error(f"三种方式都无法成功跳转到BSS系统")
                        self.logger.info(f"响应内容片段: {agent_resp.text[:500]}")
                        return False
            
            # 检查是否成功跳转到BSS
            if "bss.phone580.com" not in agent_resp.url and "bss.phone580.com" not in bss_resp.url and "bss.phone580.com" not in fzs_resp.url:
                self.logger.error(f"没有成功跳转到BSS系统，当前URL: {agent_resp.url}")
                self.logger.info(f"响应内容片段: {agent_resp.text[:500]}")
                return False
                
            self.logger.info(f"成功跳转到BSS")
            
            # 获取并保存所有cookies
            cookies_dict = requests.utils.dict_from_cookiejar(session.cookies)
            
            # 记录获取到的所有cookie
            self.logger.info(f"获取到的cookies: {cookies_dict}")
            
            if not cookies_dict:
                self.logger.error("未能获取到任何cookie")
                return False
                
            # 将cookies转换为需要的格式并保存
            cookies_list = []
            
            for name, value in cookies_dict.items():
                cookie_info = {
                    "name": name,
                    "value": value,
                    "domain": "bss.phone580.com",
                    "path": "/",
                    "hostOnly": True,
                    "httpOnly": name == "JSESSIONID",  # 通常JSESSIONID是httpOnly
                    "secure": False,
                    "session": True,
                    "storeId": None
                }
                
                # 某些cookie可能有额外属性
                if name in ["SESSION_ID", "authToken", "authTokens", "APP_ID"]:
                    expiry_time = datetime.now() + timedelta(days=90)
                    expiry_timestamp = (expiry_time - datetime(1970, 1, 1)).total_seconds()
                    cookie_info["expirationDate"] = expiry_timestamp
                    cookie_info["session"] = False
                
                cookies_list.append(cookie_info)
            
            # 添加一些从hivecookie.json获取的通用cookie
            try:
                with open(os.path.join(os.path.dirname(__file__), 'hivecookie.json'), 'r', encoding='utf-8') as f:
                    hive_cookies = json.load(f)
                    
                for hcookie in hive_cookies:
                    # 只添加一些通用domain的cookie
                    if hcookie.get('domain') == '.phone580.com':
                        # 检查cookies_list中是否已存在该cookie
                        if not any(c.get('name') == hcookie.get('name') and c.get('domain') == hcookie.get('domain') for c in cookies_list):
                            cookies_list.append(hcookie)
            except Exception as e:
                self.logger.warning(f"从hivecookie.json添加通用cookie时出错: {str(e)}")
            
            # 保存cookies到cookie.json
            with open(os.path.join(os.path.dirname(__file__), 'cookie.json'), 'w', encoding='utf-8') as f:
                json.dump(cookies_list, f, ensure_ascii=False, indent=4)
                
            self.logger.info("成功保存cookie到cookie.json文件")
            
            # 更新当前实例的cookie
            self.fengzs_cookie = '; '.join([f"{cookie['name']}={cookie['value']}" for cookie in cookies_list])
            
            return True
            
        except Exception as e:
            self.logger.error(f"登录过程出错: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())
            return False

    def load_cookies(self) -> str:
        """从cookie.json文件加载cookie"""
        try:
            cookie_path = os.path.join(os.path.dirname(__file__), 'cookie.json')
            
            # 检查cookie.json文件是否存在
            if not os.path.exists(cookie_path):
                self.logger.error("未找到cookie.json文件")
                return ''
            
            # 加载cookie文件
            with open(cookie_path, 'r', encoding='utf-8') as f:
                cookies = json.load(f)
            
            # 检查cookie是否为空列表或格式不正确
            if not cookies or not isinstance(cookies, list):
                self.logger.error("cookie.json文件格式不正确或为空")
                return ''
            
            # 生成cookie字符串
            cookie_str = '; '.join([f"{cookie['name']}={cookie['value']}" for cookie in cookies])
            
            self.logger.info("成功加载cookie")
            return cookie_str
            
        except Exception as e:
            self.logger.error(f"加载cookie失败: {str(e)}")
            return ''

    def setup_logging(self, verbose=False):
        """设置日志"""
        self.logger = logging.getLogger('FengZSCollector')
        self.logger.setLevel(logging.DEBUG if verbose else logging.INFO)
    
        # 创建日志目录（使用绝对路径）
        script_dir = os.path.dirname(os.path.abspath(__file__))
        log_dir = os.path.join(script_dir, 'logs')
        os.makedirs(log_dir, exist_ok=True)
        
        # 获取当前时间，用于日志文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 文件处理器（每次运行创建新文件）
        log_file = os.path.join(log_dir, f'collector_{timestamp}.log')
        file_handler = logging.FileHandler(
            log_file,
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG if verbose else logging.INFO)
        
        # 请求日志文件处理器
        request_log_file = os.path.join(log_dir, f'request_{timestamp}.log')
        request_handler = logging.FileHandler(
            request_log_file,
            encoding='utf-8'
        )
        request_handler.setLevel(logging.DEBUG)
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # 设置日志格式
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        request_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        # 清除已有处理器（防止重复添加）
        if self.logger.handlers:
            self.logger.handlers.clear()
            
        # 添加处理器
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
        self.logger.addHandler(request_handler)
        
        # 设置requests库的日志
        if verbose:
            requests_logger = logging.getLogger('requests')
            requests_logger.setLevel(logging.DEBUG)
            requests_logger.addHandler(request_handler)
            
            urllib3_logger = logging.getLogger('urllib3')
            urllib3_logger.setLevel(logging.DEBUG)
            urllib3_logger.addHandler(request_handler)
            
            # 记录启用了请求日志
            self.logger.info("请求详细日志已启用，将记录到request.log文件")



    def get_channel_ids(self) -> List[str]:
        """获取所有渠道ID"""
        self.logger.info("开始获取渠道ID列表")
        url = f"{self.directus_api_url}/items/channel_config"
        headers = {
            'Authorization': f'Bearer {self.directus_token}'
        }
        params = {
            'fields': 'channel_id'
        }
        
        try:
            response = requests.get(url, headers=headers, params=params)
            response.raise_for_status()
            data = response.json()
            channel_ids = [item['channel_id'] for item in data.get('data', [])]
            self.logger.info(f"成功获取到 {len(channel_ids)} 个渠道ID")
            return channel_ids
        except Exception as e:
            self.logger.error(f"获取渠道ID失败: {str(e)}")
            return []

    def get_orders_data(self, channel_id: str) -> List[Dict]:
        """获取订单数据"""
        self.logger.info(f"开始获取渠道 {channel_id} 的订单数据")
        url = f"{self.fengzs_api_url}/order-query/recharge/list"
        headers = {
            'Content-Type': 'application/json;charset=UTF-8',
            'Cookie': self.fengzs_cookie
        }
        
        all_orders = []
        page = 1
        page_size = 100
        retry_count = 0
        max_retries = 3
        
        while True:
            # 只对凤之树 API 添加延时（第一页不延时，从第二页开始延时）
            if page > 1:
                self.logger.debug(f"分页请求延时 {self.page_interval} 秒")
                time.sleep(self.page_interval)

            payload = {
                "timeType": "1",
                "channelId": channel_id,
                "beginTime": self.begin_date,
                "endTime": self.end_date,
                "businessType": "2",
                "page": page,
                "number": page_size
            }
            
            try:
                self.logger.info(f"请求渠道 {channel_id} 第 {page} 页数据")
                
                response = requests.post(url, headers=headers, json=payload)
                
                if self.verbose_logging:
                    self.logger.info(f"响应状态码: {response.status_code}")
                
                # 检查是否被重定向到登录页面
                if "login" in response.text.lower() or "登录" in response.text:
                    self.logger.warning(f"请求返回登录页面，渠道 {channel_id} 数据获取失败，可能是cookie失效")
                    self.logger.warning("请检查cookie.json文件是否有效")
                    if retry_count < max_retries:
                        retry_count += 1
                        self.logger.info(f"重试 ({retry_count}/{max_retries})")
                        time.sleep(1)  # 减少重试延时
                        continue
                    else:
                        break
                
                response.raise_for_status()
                
                try:
                    data = response.json()
                except json.JSONDecodeError as json_err:
                    self.logger.error(f"解析JSON失败: {str(json_err)}")
                    self.logger.info(f"响应内容前100个字符: {response.text[:100]}")
                    if retry_count < max_retries:
                        retry_count += 1
                        self.logger.info(f"重试解析JSON ({retry_count}/{max_retries})")
                        time.sleep(1)  # 减少JSON解析重试延时
                        continue
                    else:
                        break
                
                # 检查响应中是否包含data字段
                if 'data' not in data:
                    self.logger.warning(f"响应中没有data字段: {data}")
                    if retry_count < max_retries:
                        retry_count += 1
                        self.logger.info(f"重试获取data字段 ({retry_count}/{max_retries})")
                        time.sleep(1)  # 减少数据字段重试延时
                        continue
                    else:
                        break
                
                orders = data.get('data', [])
                
                if not orders:
                    self.logger.info(f"渠道 {channel_id} 第 {page} 页无数据，结束分页")
                    break
                
                # 记录详细的订单ID，并检查空值
                order_ids = []
                empty_order_count = 0
                for order in orders:
                    order_no = order.get('orderNo', 'unknown')
                    if not order_no or order_no in [None, '', 'null', 'undefined']:
                        empty_order_count += 1
                        order_ids.append(f"EMPTY_{empty_order_count}")
                    else:
                        order_ids.append(order_no)

                self.logger.info(f"渠道 {channel_id} 第 {page} 页获取到订单IDs: {', '.join(order_ids)}")
                if empty_order_count > 0:
                    self.logger.warning(f"渠道 {channel_id} 第 {page} 页发现 {empty_order_count} 个空订单号")
                    
                all_orders.extend(orders)
                self.logger.info(f"渠道 {channel_id} 第 {page} 页获取到 {len(orders)} 条订单数据")
                
                if len(orders) < page_size:
                    self.logger.info(f"渠道 {channel_id} 数据不足一页，结束分页")
                    break
                
                page += 1
                retry_count = 0  # 成功获取数据后重置重试计数
                # 分页延时已在循环开始时处理，这里不需要重复延时
                
            except Exception as e:
                self.logger.error(f"获取订单数据失败: {str(e)}")
                if hasattr(e, 'response') and e.response is not None:
                    self.logger.error(f"响应状态码: {e.response.status_code}")
                    if hasattr(e.response, 'text'):
                        self.logger.error(f"响应内容前100个字符: {e.response.text[:100]}")
                
                # 尝试重试
                if retry_count < max_retries:
                    retry_count += 1
                    self.logger.info(f"重试请求 ({retry_count}/{max_retries})")
                    time.sleep(2)  # 减少异常重试延时
                    continue
                break
        
        return all_orders

    def get_channel_config_map(self) -> Dict[str, Dict]:
        """获取渠道配置信息的映射关系，包括id、channel_id、channel_name和description"""
        self.logger.info("开始获取渠道配置映射")
        url = f"{self.directus_api_url}/items/channel_config"
        headers = {
            'Authorization': f'Bearer {self.directus_token}'
        }
        params = {
            'fields': ['id', 'channel_id', 'channel_name', 'description']
        }
        
        try:
            response = requests.get(url, headers=headers, params=params)
            response.raise_for_status()
            data = response.json().get('data', [])
            # 创建channel_id到完整配置信息的映射
            channel_map = {
                item['channel_id']: {
                    'id': item['id'],
                    'channel_name': item.get('channel_name', ''),
                    'description': item.get('description', '')
                } for item in data
            }
            self.logger.info(f"成功获取到 {len(channel_map)} 个渠道配置映射")
            return channel_map
        except Exception as e:
            self.logger.error(f"获取渠道配置映射失败: {str(e)}")
            return {}

    def send_wecom_notification(self, total_orders: int, duration: float, insert_orders: int, update_orders: int, failed_orders: int):
        """发送企业微信通知"""
        if not self.webhook_url:
            self.logger.warning("未配置企业微信 webhook 地址，跳过通知")
            return

        # 准备时间范围显示
        date_range = f"{self.original_begin_date[:10]}"
        if self.original_begin_date[:10] != self.original_end_date[:10]:
            date_range += f" 至 {self.original_end_date[:10]}"

        message = {
            "msgtype": "text",
            "text": {
                "content": f"📊 凤之树数据采集报告\n━━━━━━━━━━━━━━\n⏰ 数据日期：{date_range}\n🔢 数据情况：新增{insert_orders}条、更新{update_orders}条、失败{failed_orders}条\n⌛ 耗时：{duration:.1f}秒"
            }
        }

        try:
            response = requests.post(self.webhook_url, json=message)
            response.raise_for_status()
            self.logger.info("企业微信通知发送成功")
        except Exception as e:
            self.logger.error(f"发送企业微信通知失败: {str(e)}")

    def split_date_range(self, begin_date_str: str, end_date_str: str, max_days: int = 30) -> List[Tuple[str, str]]:
        """
        将时间区间分割为多个最长不超过max_days天的子区间
        
        Args:
            begin_date_str: 开始日期字符串，格式: YYYY-MM-DD HH:MM:SS
            end_date_str: 结束日期字符串，格式: YYYY-MM-DD HH:MM:SS
            max_days: 每个子区间的最大天数
            
        Returns:
            List[Tuple[str, str]]: 分割后的时间区间列表，每个元素为(开始时间, 结束时间)
        """
        # 解析日期字符串
        date_format = '%Y-%m-%d %H:%M:%S'
        begin_date = datetime.strptime(begin_date_str, date_format)
        end_date = datetime.strptime(end_date_str, date_format)
        
        # 如果总时间不超过max_days天，直接返回原始区间
        total_days = (end_date - begin_date).days + 1
        if total_days <= max_days:
            self.logger.info(f"时间区间不超过{max_days}天，不需要分割")
            return [(begin_date_str, end_date_str)]
        
        # 分割时间区间
        date_ranges = []
        current_begin = begin_date
        
        while current_begin <= end_date:
            # 计算当前子区间的结束时间
            current_end = min(current_begin + timedelta(days=max_days-1), end_date)
            
            # 确保结束时间为当天的23:59:59
            current_end = datetime.combine(current_end.date(), datetime.max.time()).replace(microsecond=0)
            
            # 如果结束时间超过了总结束时间，则使用总结束时间
            if current_end > end_date:
                current_end = end_date
                
            # 将时间对象转换为字符串并添加到结果列表
            begin_str = current_begin.strftime(date_format)
            end_str = current_end.strftime(date_format)
            date_ranges.append((begin_str, end_str))
            
            self.logger.info(f"分割的时间区间: {begin_str} 至 {end_str}")
            
            # 移动到下一个区间的开始
            current_begin = current_end + timedelta(seconds=1)
        
        self.logger.info(f"时间区间已分割为{len(date_ranges)}个子区间")
        return date_ranges

    def run(self):
        """主运行函数"""
        # 如果是仅测试登录模式，则不执行爬虫操作
        if self.test_login_only:
            self.logger.info("仅测试登录模式，不执行爬虫操作")
            return
            
        # 现在只有在当次登录成功时才会执行爬虫操作
        if not self.login_successful:
            self.logger.error("登录失败，无法执行爬虫操作")
            return
            
        try:
            self.logger.info("开始数据采集任务")
            start_time = datetime.now()
            
            # 获取渠道配置映射（Directus API，不需要延时）
            channel_config_map = self.get_channel_config_map()
            channel_ids = list(channel_config_map.keys())
            
            if not channel_ids:
                self.logger.warning("未获取到任何渠道ID，请检查Directus配置")
                return
            
            # 分割时间区间
            date_ranges = self.split_date_range(self.original_begin_date, self.original_end_date)
            
            total_orders = 0
            insert_orders = 0
            update_orders = 0
            failed_orders = 0
            
            # 遍历每个时间区间
            for range_index, (begin_date, end_date) in enumerate(date_ranges):
                # 检查停止信号
                if self.check_stop_signal():
                    self.logger.info("收到停止信号，中断数据采集")
                    break

                # 更新当前使用的时间区间
                self.begin_date = begin_date
                self.end_date = end_date
                self.logger.info(f"处理时间区间 [{range_index+1}/{len(date_ranges)}]: {begin_date} 至 {end_date}")
                
                # 遍历每个渠道ID
                for i, channel_id in enumerate(channel_ids):
                    # 检查停止信号
                    if self.check_stop_signal():
                        self.logger.info("收到停止信号，中断渠道处理")
                        break

                    try:
                        self.logger.info(f"开始处理渠道 [{i+1}/{len(channel_ids)}]: {channel_id}")
                        # 获取订单数据（凤之树 API，需要延时）
                        raw_orders = self.get_orders_data(channel_id)

                        if not raw_orders:
                            self.logger.warning(f"渠道 {channel_id} 在时间区间 {begin_date} 至 {end_date} 未获取到任何订单数据")
                            continue

                        # 过滤有效订单
                        orders = self.filter_valid_orders(raw_orders)
                        total_orders += len(orders)

                        if not orders:
                            self.logger.warning(f"渠道 {channel_id} 在过滤后没有有效订单数据")
                            continue
                        
                        # 使用批量处理提高性能
                        batch_size = 100  # 增加批量大小
                        total_batches = (len(orders) + batch_size - 1) // batch_size

                        for j in range(0, len(orders), batch_size):
                            batch_orders = orders[j:j+batch_size]
                            batch_num = j//batch_size + 1
                            self.logger.info(f"批量处理渠道 {channel_id} 的第 {batch_num}/{total_batches} 批订单，共 {len(batch_orders)} 条")

                            # 准备批量数据
                            batch_data = []
                            for order in batch_orders:
                                try:
                                    # 首先检查orderNo是否有效
                                    order_no = order.get('orderNo')
                                    if not order_no or order_no in [None, '', 'null', 'undefined']:
                                        failed_orders += 1
                                        self.logger.warning(f"跳过无效订单号的订单: {order_no}, 原始数据: {order}")
                                        continue

                                    # 添加渠道配置信息
                                    channel_info = channel_config_map.get(channel_id, {})
                                    order['channel_config'] = channel_info.get('id')
                                    order['channel_id'] = channel_id
                                    order['channel_name'] = channel_info.get('channel_name', '')
                                    order['description'] = channel_info.get('description', '')

                                    # 准备订单数据
                                    order_data = self.prepare_order_data(order)
                                    batch_data.append(order_data)

                                except ValueError as ve:
                                    # 处理orderNo验证错误
                                    failed_orders += 1
                                    self.logger.warning(f"订单数据验证失败: {str(ve)}")
                                except Exception as e:
                                    failed_orders += 1
                                    self.logger.error(f"准备订单数据失败: {str(e)}")

                            # 批量入库
                            if batch_data:
                                try:
                                    batch_start_time = time.time()
                                    result = self.directus_client.batch_upsert_items(
                                        collection='forders',
                                        items=batch_data,
                                        primary_key_field='orderNo'
                                    )
                                    batch_time = time.time() - batch_start_time

                                    insert_orders += result['created']
                                    update_orders += result['updated']
                                    failed_orders += result['failed']

                                    self.logger.info(f"批量入库完成: 新增{result['created']}条, 更新{result['updated']}条, 失败{result['failed']}条, 耗时{batch_time:.2f}秒")

                                except Exception as batch_e:
                                    self.logger.error(f"批量入库失败，回退到单条处理: {str(batch_e)}")
                                    # 回退到单条处理
                                    for order_data in batch_data:
                                        try:
                                            order_no = order_data.get('orderNo')
                                            is_update = self.upsert_order_data(order_data)
                                            if is_update:
                                                update_orders += 1
                                            else:
                                                insert_orders += 1
                                        except Exception as e:
                                            failed_orders += 1
                                            self.logger.error(f"单条处理订单失败: {str(e)}")
                
                    except Exception as channel_e:
                        self.logger.error(f"处理渠道 {channel_id} 时出错: {str(channel_e)}")
                        # 继续处理下一个渠道
                        
                    # 只在处理凤之树 API 请求之间添加延时（入库操作不延时）
                    if i < len(channel_ids) - 1:  # 如果不是最后一个渠道
                        self.logger.debug(f"渠道间延时 {self.channel_interval} 秒")
                        time.sleep(self.channel_interval)
                
                # 在处理不同时间区间之间增加短暂间隔（减少延时）
                if range_index < len(date_ranges) - 1:
                    self.logger.info("时间区间处理完毕，休息2秒后继续下一个时间区间")
                    time.sleep(2)
                    
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            # 发送企业微信通知，添加更多参数
            self.send_wecom_notification(total_orders, duration, insert_orders, update_orders, failed_orders)
            
            date_summary = f"{self.original_begin_date} 至 {self.original_end_date}"
            if len(date_ranges) > 1:
                date_summary += f"（分{len(date_ranges)}个区间）"
                
            self.logger.info(f"""
            数据任务已完成:
            - 总耗时: {duration:.2f} 秒
            - 时间区间: {date_summary}
            - 处理渠道数: {len(channel_ids)}
            - 总订单数: {total_orders}
            - 新增订单: {insert_orders}
            - 更新订单: {update_orders}
            - 失败订单: {failed_orders}
            """)
                
        except Exception as e:
            self.logger.error(f"任务执行出错: {str(e)}")
            # 发送错误通知
            try:
                self.send_error_notification(str(e))
            except:
                pass
            raise  # 其他错误直接抛出

    def send_error_notification(self, error_message):
        """发送错误通知到企业微信"""
        if not self.webhook_url:
            return
            
        try:
            message = {
                "msgtype": "text",
                "text": {
                    "content": f"❌ 凤之树数据采集失败\n━━━━━━━━━━━━━━\n⏰ 日期：{self.original_begin_date[:10]} 至 {self.original_end_date[:10]}\n🔴 错误：{error_message}"
                }
            }
            
            response = requests.post(self.webhook_url, json=message)
            response.raise_for_status()
            self.logger.info("错误通知已发送")
        except Exception as e:
            self.logger.error(f"发送错误通知失败: {str(e)}")

    def parse_send_goods_result(self, result: str) -> Dict:
        """解析发货结果"""
        try:
            # 处理完整的 JSON 响应
            if result.startswith('respCode=200, respBody='):
                # 提取 respBody 部分
                json_str = result.replace('respCode=200, respBody=', '')
                return json.loads(json_str)
            # 处理发货回调的情况
            elif result.startswith('发货回调'):
                try:
                    # 提取 JSON 字符串部分
                    start = result.find('{')
                    if start != -1:
                        json_str = result[start:]
                        return json.loads(json_str)
                except:
                    pass
            # 如果无法解析，返回原始字符串
            return {'raw': result}
        except Exception as e:
            self.logger.error(f"解析发货结果失败: {str(e)}")
            return {'raw': result, 'error': str(e)}
    
    def extract_send_goods_result_alias(self, parsed_result: Dict) -> str:
        """从解析后的发货结果中提取重要描述信息"""
        try:
            # 处理情况1: resultCode=0004的情况
            if parsed_result.get('resultCode') == '0004' and 'details' in parsed_result:
                return parsed_result.get('details', '')
                
            # 处理情况2: 发货回调的复杂情况
            if 'raw' in parsed_result and isinstance(parsed_result['raw'], str) and '发货回调' in parsed_result['raw']:
                # 尝试从发货回调中提取statusDesc
                try:
                    # 查找statusDesc值
                    text = parsed_result['raw']
                    status_desc_index = text.find('"statusDesc"')
                    if status_desc_index != -1:
                        # 找到statusDesc后寻找值的开始位置
                        value_start = text.find('"', status_desc_index + 13) # "statusDesc"之后的第一个引号
                        if value_start != -1:
                            value_end = text.find('"', value_start + 1) # 值之后的第一个引号
                            if value_end != -1:
                                # 提取值并转义JSON字符串中的转义符
                                status_desc = text[value_start+1:value_end].replace('\\\\', '\\').replace('\\"', '"')
                                return status_desc
                except Exception as e:
                    self.logger.error(f"从发货回调中提取statusDesc失败: {str(e)}")
            
            return ''
        except Exception as e:
            self.logger.error(f"提取发货结果描述失败: {str(e)}")
            return ''
    
    def map_business_status(self, status: str) -> str:
        """将英文业务状态映射为中文状态"""
        status_map = {
            'FINISH': '交易成功',
            'TO_BE_SHIP': '待发货',
            'SHIPPING': '发货中',
            'REFUNDOK': '已退款'
        }
        return status_map.get(status, status)
    
    def map_out_status(self, status: str) -> str:
        """将英文出货状态映射为中文状态"""
        status_map = {
            'ACTIVEOK': '已激活'
        }
        return status_map.get(status, status)

    def map_logistics_status(self, status: str) -> str:
        """将英文物流状态映射为中文状态"""
        status_map = {
            'UNSHIP': '未发货',
            'SHIPPING': '发货中',
            'SHIPOK': '发货成功',
            'SHIPFAILED': '发货失败'
        }
        return status_map.get(status, status)

    def validate_order_data(self, order: Dict) -> bool:
        """验证订单数据是否有效"""
        # 检查必需的字段
        order_no = order.get('orderNo')
        if not order_no or order_no in [None, '', 'null', 'undefined']:
            self.logger.warning(f"订单号无效: {order_no}")
            return False

        # 可以添加其他验证逻辑
        return True

    def filter_valid_orders(self, orders: List[Dict]) -> List[Dict]:
        """过滤出有效的订单数据"""
        valid_orders = []
        invalid_count = 0

        for order in orders:
            if self.validate_order_data(order):
                valid_orders.append(order)
            else:
                invalid_count += 1

        if invalid_count > 0:
            self.logger.warning(f"过滤掉 {invalid_count} 个无效订单，保留 {len(valid_orders)} 个有效订单")

        return valid_orders

    def prepare_order_data(self, order: Dict) -> Dict:
        """准备订单数据用于批量处理"""
        # 首先验证orderNo是否存在且不为空
        order_no = order.get('orderNo')
        if not order_no or order_no in [None, '', 'null', 'undefined']:
            raise ValueError(f"订单号为空或无效: {order_no}")

        # 解析发货结果
        send_goods_result = order.get('sendGoodsResult', '')
        parsed_result = self.parse_send_goods_result(send_goods_result)

        # 提取发货结果描述
        send_goods_result_alias = self.extract_send_goods_result_alias(parsed_result)

        # 状态映射转换
        business_status = self.map_business_status(order.get('businessStatus', ''))
        out_status = self.map_out_status(order.get('outStatus', ''))
        logistics_status = self.map_logistics_status(order.get('logisticsStatus', ''))

        # 准备要插入/更新的数据
        order_data = {
            'orderNo': order_no,  # 使用验证过的orderNo
            'outStatus': out_status,
            'businessStatus': business_status,
            'createTime': order.get('createTime'),
            'updateTime': order.get('updateTime'),
            'deliveryTime': order.get('deliveryTime'),
            'channel_config': order.get('channel_config'),
            'target': order.get('target'),  # 手机号字段
            'send_goods_result': json.dumps(parsed_result, ensure_ascii=False),  # 发货结果
            'Phone_Card': order.get('skuCode'),  # 现在skuCode作为Phone_Card外键
            'send_goods_result_Alias': send_goods_result_alias,  # 发货结果描述
            'logisticsStatus': logistics_status  # 物流状态
        }

        return order_data

    def upsert_order_data(self, order_data: Dict) -> bool:
        """使用准备好的数据进行upsert操作"""
        order_no = order_data.get('orderNo')

        try:
            # 使用Directus客户端进行upsert操作
            result_data, is_update = self.directus_client.upsert_item(
                collection='forders',
                primary_key=order_no,
                data=order_data
            )

            return is_update

        except Exception as e:
            self.logger.error(f"处理订单 {order_no} 失败: {str(e)}")
            raise

    def upsert_order(self, order: Dict) -> bool:
        """单个订单upsert操作（兼容性方法）"""
        order_no = order.get('orderNo')
        self.logger.info(f"处理订单 {order_no}")

        try:
            # 准备订单数据
            order_data = self.prepare_order_data(order)

            # 记录详情
            self.logger.debug(f"订单 {order_no} 详情: 业务状态={order_data['businessStatus']}, 出货状态={order_data['outStatus']}, 物流状态={order_data['logisticsStatus']}")

            # 执行upsert
            is_update = self.upsert_order_data(order_data)

            if self.verbose_logging:
                if is_update:
                    self.logger.info(f"订单 {order_no} 更新成功")
                else:
                    self.logger.info(f"订单 {order_no} 新增成功")

            return is_update

        except Exception as e:
            self.logger.error(f"处理订单 {order_no} 失败: {str(e)}")
            raise

    def login_with_retry(self) -> bool:
        """带重试功能的登录方法"""
        max_retries = self.login_retry_count
        retry_count = 0
        base_delay = 2  # 基础延迟时间，单位为秒
        
        while retry_count < max_retries:
            if retry_count > 0:
                # 计算递增的等待时间
                delay = base_delay * (retry_count * 1.5)
                self.logger.info(f"登录重试 ({retry_count + 1}/{max_retries})，等待 {delay:.1f} 秒后重试...")
                print(f"\n正在进行第 {retry_count + 1} 次登录尝试，等待 {delay:.1f} 秒...\n")
                time.sleep(delay)
            else:
                self.logger.info("首次登录尝试")
            
            # 执行登录
            success = self.perform_login()
            
            if success:
                self.logger.info(f"登录成功！尝试次数: {retry_count + 1}")
                if retry_count > 0:
                    print(f"\n✅ 第 {retry_count + 1} 次尝试登录成功！\n")
                return True
            
            retry_count += 1
            
            if retry_count < max_retries:
                self.logger.warning(f"登录失败 ({retry_count}/{max_retries})，将重试...")
            
        self.logger.error(f"登录失败，已达到最大重试次数 ({max_retries})")
        return False

if __name__ == "__main__":
    # 添加命令行参数支持
    parser = argparse.ArgumentParser(description='凤之树数据采集工具')
    parser.add_argument('--begin-date', help='开始日期，格式：YYYY-MM-DD HH:MM:SS')
    parser.add_argument('--end-date', help='结束日期，格式：YYYY-MM-DD HH:MM:SS')
    parser.add_argument('--verbose', '-v', action='store_true', help='启用详细日志记录，包括HTTP请求和响应')
    parser.add_argument('--channel-interval', type=float, help='渠道间请求间隔，单位：秒，默认从环境变量获取')
    parser.add_argument('--page-interval', type=float, help='分页请求间隔，单位：秒，默认从环境变量获取')
    parser.add_argument('--no-auto-login', action='store_true', help='禁用自动登录功能')
    parser.add_argument('--test-login', action='store_true', help='仅测试登录功能，成功后退出')
    parser.add_argument('--retry', type=int, default=3, help='登录失败时的最大重试次数')
    parser.add_argument('--agent-id', help='指定用于SSO跳转的agent_id，默认从环境变量获取')
    args = parser.parse_args()

    # 检查.env文件是否存在
    env_file = os.path.join(os.path.dirname(__file__), '.env')
    if not os.path.exists(env_file):
        print("\n警告：未找到.env配置文件。程序可能无法正常运行。")
        print("请创建.env文件并添加必要的配置项。\n")
        
        # 询问用户是否要继续
        try:
            response = input("是否继续？(y/n): ").strip().lower()
            if response != 'y':
                print("已取消操作。")
                sys.exit(0)
        except KeyboardInterrupt:
            print("\n已取消操作。")
            sys.exit(0)
    
    # 检查hivecookie.json文件是否存在
    hivecookie_path = os.path.join(os.path.dirname(__file__), 'hivecookie.json')
    if not os.path.exists(hivecookie_path):
        print("\n警告：未找到hivecookie.json文件。程序可能无法正常进行SSO登录。")
        print("请确保hivecookie.json文件存在且包含有效的cookie。\n")
        
        # 询问用户是否要继续
        try:
            response = input("是否继续？(y/n): ").strip().lower()
            if response != 'y':
                print("已取消操作。")
                sys.exit(0)
        except KeyboardInterrupt:
            print("\n已取消操作。")
            sys.exit(0)
    
    # 设置环境变量（如果命令行有指定）
    if args.begin_date:
        os.environ['BEGIN_DATE'] = args.begin_date
    if args.end_date:
        os.environ['END_DATE'] = args.end_date
    if args.channel_interval is not None:
        os.environ['CHANNEL_INTERVAL'] = str(args.channel_interval)
    if args.page_interval is not None:
        os.environ['PAGE_INTERVAL'] = str(args.page_interval)
    if args.agent_id:
        os.environ['AGENT_ID'] = args.agent_id
    
    # 创建采集器实例，传入命令行参数
    collector = FengZSCollector(
        verbose_logging=args.verbose,
        channel_interval=args.channel_interval,
        page_interval=args.page_interval,
        auto_login=not args.no_auto_login,
        test_login_only=args.test_login,
        login_retry_count=args.retry
    )
    
    # 运行采集器
    try:
        collector.run()
        if not args.test_login:  # 仅在非测试模式下显示完成信息
            print("\n✅ 数据采集任务完成！")
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断操作，程序已停止")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ 数据采集失败: {str(e)}")
        sys.exit(1)