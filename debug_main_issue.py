#!/usr/bin/env python3
"""
调试主程序问题
"""

import os
import sys
import time
import json
from dotenv import load_dotenv
from directus_client import DirectusClient
import logging
import inspect

# 加载环境变量
load_dotenv()

def debug_main_issue():
    """调试主程序问题"""
    logging.basicConfig(level=logging.DEBUG)
    logger = logging.getLogger(__name__)
    
    logger.info("调试主程序问题...")
    
    # 获取配置
    directus_api_url = os.getenv('DIRECTUS_API_URL')
    directus_token = os.getenv('DIRECTUS_TOKEN')
    
    if not directus_api_url or not directus_token:
        logger.error("缺少Directus配置信息")
        return False
    
    try:
        # 创建客户端
        client = DirectusClient(
            api_url=directus_api_url,
            token=directus_token,
            logger=logger
        )
        
        # 检查batch_upsert_items方法的源码
        method_source = inspect.getsource(client.batch_upsert_items)
        logger.info(f"batch_upsert_items方法源码包含'真正的upsert版本': {'真正的upsert版本' in method_source}")
        
        # 模拟主程序的批量处理逻辑
        logger.info("模拟主程序的批量处理...")
        
        # 创建一些测试数据（模拟已存在的订单）
        test_data = []
        for i in range(3):
            order_data = {
                'orderNo': f'DEBUG_MAIN_{int(time.time())}_{i:03d}',
                'outStatus': '已激活',
                'businessStatus': '交易成功',
                'createTime': '2025-01-01T00:00:00',
                'updateTime': '2025-01-01T00:00:00',
                'target': f'138{i:08d}',
                'send_goods_result': json.dumps({"test": f"debug_data_{i}"}, ensure_ascii=False),
                'send_goods_result_Alias': f'调试测试描述_{i}',
                'logisticsStatus': '发货成功',
                'channel_config': 5,
                'Phone_Card': 'BJYD_58Y_SFQSK'
            }
            test_data.append(order_data)
        
        # 先创建这些订单（模拟已存在的情况）
        logger.info("先创建订单（模拟已存在的情况）...")
        for order_data in test_data:
            try:
                client.create_item('forders', order_data)
                logger.info(f"创建订单: {order_data['orderNo']}")
            except Exception as e:
                logger.info(f"订单可能已存在: {order_data['orderNo']} - {str(e)}")
        
        # 现在模拟主程序的批量upsert逻辑
        logger.info("执行批量upsert（模拟主程序逻辑）...")
        
        batch_start_time = time.time()
        result = client.batch_upsert_items(
            collection='forders',
            items=test_data,
            primary_key_field='orderNo'
        )
        batch_time = time.time() - batch_start_time
        
        logger.info(f"批量upsert结果:")
        logger.info(f"  创建: {result['created']} 条")
        logger.info(f"  更新: {result['updated']} 条")
        logger.info(f"  失败: {result['failed']} 条")
        logger.info(f"  耗时: {batch_time:.2f}秒")
        
        # 清理测试数据
        logger.info("清理测试数据...")
        for order_data in test_data:
            try:
                client.delete_item('forders', order_data['orderNo'])
            except Exception as e:
                logger.warning(f"删除失败: {order_data['orderNo']} - {str(e)}")
        
        # 判断是否成功
        if result['failed'] == 0 and result['updated'] == len(test_data):
            logger.info("🎉 主程序逻辑测试成功！")
            return True
        else:
            logger.error("❌ 主程序逻辑测试失败")
            return False
        
    except Exception as e:
        logger.error(f"调试失败: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    success = debug_main_issue()
    sys.exit(0 if success else 1)
