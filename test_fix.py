#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的 fengzs_collector.py 程序
"""

import json
from fengzs_collector import FengZSCollector

def test_order_validation():
    """测试订单数据验证功能"""
    print("测试订单数据验证功能...")
    
    # 创建一个测试用的采集器实例（不自动登录）
    collector = FengZSCollector(auto_login=False)
    
    # 测试数据
    test_orders = [
        {'orderNo': 'ORDER001', 'businessStatus': 'FINISH'},  # 有效订单
        {'orderNo': None, 'businessStatus': 'FINISH'},        # 无效订单 - None
        {'orderNo': '', 'businessStatus': 'FINISH'},          # 无效订单 - 空字符串
        {'orderNo': 'ORDER002', 'businessStatus': 'TO_BE_SHIP'},  # 有效订单
        {'orderNo': 'null', 'businessStatus': 'FINISH'},      # 无效订单 - 字符串null
        {'businessStatus': 'FINISH'},                         # 无效订单 - 缺少orderNo
    ]
    
    print(f"原始订单数量: {len(test_orders)}")
    
    # 测试过滤功能
    valid_orders = collector.filter_valid_orders(test_orders)
    print(f"有效订单数量: {len(valid_orders)}")
    
    # 显示有效订单
    for order in valid_orders:
        print(f"有效订单: {order['orderNo']}")
    
    # 测试prepare_order_data方法
    print("\n测试prepare_order_data方法...")
    for i, order in enumerate(test_orders):
        try:
            # 添加必需的字段
            order['channel_config'] = 1
            order['channel_id'] = 'test_channel'
            order['channel_name'] = '测试渠道'
            order['description'] = '测试描述'
            
            order_data = collector.prepare_order_data(order)
            print(f"订单 {i+1} 准备成功: {order_data['orderNo']}")
        except ValueError as e:
            print(f"订单 {i+1} 验证失败: {e}")
        except Exception as e:
            print(f"订单 {i+1} 处理失败: {e}")

def test_directus_error_handling():
    """测试Directus客户端的错误处理"""
    print("\n测试Directus客户端错误处理...")
    
    from directus_api_client import DirectusClient
    import logging
    
    # 创建测试日志
    logger = logging.getLogger('test')
    logger.setLevel(logging.DEBUG)
    handler = logging.StreamHandler()
    handler.setFormatter(logging.Formatter('%(levelname)s - %(message)s'))
    logger.addHandler(handler)
    
    # 创建客户端（使用虚假的配置，只是为了测试错误处理逻辑）
    client = DirectusClient('http://test.com', 'fake_token', logger)
    
    # 测试主键验证
    test_cases = [
        ('valid_key', True),
        (None, False),
        ('', False),
        ('null', False),
    ]
    
    for primary_key, should_be_valid in test_cases:
        try:
            # 这里不会真正发送请求，只是测试验证逻辑
            print(f"测试主键: {primary_key} - 预期{'有效' if should_be_valid else '无效'}")
            if not primary_key or primary_key in ['None', 'null', '']:
                raise ValueError(f"主键值无效: {primary_key}")
            print("  ✓ 主键验证通过")
        except ValueError as e:
            print(f"  ✗ 主键验证失败: {e}")

if __name__ == "__main__":
    print("开始测试修复后的程序...")
    
    try:
        test_order_validation()
        test_directus_error_handling()
        print("\n✅ 所有测试完成！")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
