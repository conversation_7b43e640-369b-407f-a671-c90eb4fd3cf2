import requests
import json
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime


class DirectusClient:
    """Directus API客户端，用于处理数据的增删改查操作"""
    
    def __init__(self, api_url: str, token: str, logger: Optional[logging.Logger] = None):
        """
        初始化Directus客户端
        
        Args:
            api_url: Directus API基础URL
            token: 访问令牌
            logger: 日志记录器
        """
        self.api_url = api_url.rstrip('/')
        self.token = token
        self.logger = logger or logging.getLogger(__name__)
        
        # 设置请求头
        self.headers = {
            'Authorization': f'Bearer {self.token}',
            'Content-Type': 'application/json'
        }
    
    def _make_request(self, method: str, endpoint: str, data: Optional[Dict] = None, params: Optional[Dict] = None) -> Dict:
        """
        发送HTTP请求到Directus API
        
        Args:
            method: HTTP方法 (GET, POST, PATCH, DELETE)
            endpoint: API端点
            data: 请求数据
            params: URL参数
            
        Returns:
            API响应数据
            
        Raises:
            Exception: 请求失败时抛出异常
        """
        url = f"{self.api_url}{endpoint}"
        
        try:
            response = requests.request(
                method=method,
                url=url,
                headers=self.headers,
                json=data,
                params=params,
                timeout=30
            )
            
            # 记录请求详情
            self.logger.debug(f"{method} {url} - Status: {response.status_code}")
            
            if response.status_code >= 400:
                error_msg = f"Directus API请求失败: {method} {url} - {response.status_code}"
                if response.text:
                    try:
                        error_data = response.json()
                        error_msg += f" - {error_data}"
                    except:
                        error_msg += f" - {response.text}"
                
                self.logger.error(error_msg)
                raise Exception(error_msg)
            
            return response.json() if response.text else {}
            
        except requests.exceptions.RequestException as e:
            error_msg = f"Directus API请求异常: {method} {url} - {str(e)}"
            self.logger.error(error_msg)
            raise Exception(error_msg)
    
    def get_item(self, collection: str, item_id: str, fields: Optional[List[str]] = None) -> Optional[Dict]:
        """
        获取单个项目

        Args:
            collection: 集合名称
            item_id: 项目ID
            fields: 要返回的字段列表

        Returns:
            项目数据，如果不存在则返回None
        """
        endpoint = f"/items/{collection}/{item_id}"
        params = {}

        if fields:
            params['fields'] = ','.join(fields)

        try:
            response = self._make_request('GET', endpoint, params=params)
            return response.get('data')
        except Exception as e:
            if "404" in str(e) or "403" in str(e):
                # 404表示不存在，403可能表示权限不足或不存在
                return None
            raise
    
    def create_item(self, collection: str, data: Dict) -> Dict:
        """
        创建新项目
        
        Args:
            collection: 集合名称
            data: 项目数据
            
        Returns:
            创建的项目数据
        """
        endpoint = f"/items/{collection}"
        response = self._make_request('POST', endpoint, data=data)
        return response.get('data', {})
    
    def update_item(self, collection: str, item_id: str, data: Dict) -> Dict:
        """
        更新项目
        
        Args:
            collection: 集合名称
            item_id: 项目ID
            data: 更新数据
            
        Returns:
            更新后的项目数据
        """
        endpoint = f"/items/{collection}/{item_id}"
        response = self._make_request('PATCH', endpoint, data=data)
        return response.get('data', {})
    
    def upsert_item(self, collection: str, primary_key: str, data: Dict) -> tuple[Dict, bool]:
        """
        插入或更新项目（upsert操作）- 优化版本，先尝试创建，失败则更新

        Args:
            collection: 集合名称
            primary_key: 主键值
            data: 项目数据

        Returns:
            (项目数据, 是否为更新操作)
        """
        # 确保主键包含在数据中
        if collection == 'forders':
            if 'orderNo' not in data:
                data['orderNo'] = primary_key
        else:
            if 'id' not in data:
                data['id'] = primary_key

        # 先尝试创建（大多数情况下是新数据）
        try:
            created_item = self.create_item(collection, data)
            return created_item, False
        except Exception as create_e:
            # 如果创建失败，可能是因为主键冲突，尝试更新
            error_str = str(create_e).lower()
            # 检查多种可能的重复键错误格式
            is_duplicate_error = (
                "duplicate" in error_str or
                "unique" in error_str or
                "already exists" in error_str or
                "record_not_unique" in error_str or
                "RECORD_NOT_UNIQUE" in str(create_e)  # 检查原始大小写
            )

            if is_duplicate_error:
                self.logger.debug(f"检测到重复键错误，尝试更新: {primary_key}")
                try:
                    updated_item = self.update_item(collection, primary_key, data)
                    return updated_item, True
                except Exception as update_e:
                    self.logger.error(f"创建和更新都失败: 创建错误={str(create_e)}, 更新错误={str(update_e)}")
                    raise update_e
            else:
                # 其他创建错误，直接抛出
                self.logger.error(f"非重复键创建错误: {str(create_e)}")
                raise create_e
    
    def query_items(self, collection: str, filter_params: Optional[Dict] = None, 
                   fields: Optional[List[str]] = None, limit: Optional[int] = None) -> List[Dict]:
        """
        查询项目列表
        
        Args:
            collection: 集合名称
            filter_params: 过滤参数
            fields: 要返回的字段列表
            limit: 限制返回数量
            
        Returns:
            项目列表
        """
        endpoint = f"/items/{collection}"
        params = {}
        
        if filter_params:
            params.update(filter_params)
        
        if fields:
            params['fields'] = ','.join(fields)
        
        if limit:
            params['limit'] = limit
        
        response = self._make_request('GET', endpoint, params=params)
        return response.get('data', [])
    
    def delete_item(self, collection: str, item_id: str) -> bool:
        """
        删除项目

        Args:
            collection: 集合名称
            item_id: 项目ID

        Returns:
            是否删除成功
        """
        endpoint = f"/items/{collection}/{item_id}"

        try:
            self._make_request('DELETE', endpoint)
            return True
        except Exception as e:
            self.logger.error(f"删除项目失败: {str(e)}")
            return False

    def batch_upsert_items(self, collection: str, items: List[Dict], primary_key_field: str = 'id') -> Dict:
        """
        批量插入或更新项目（真正的upsert版本）

        Args:
            collection: 集合名称
            items: 项目数据列表
            primary_key_field: 主键字段名

        Returns:
            {'created': 创建数量, 'updated': 更新数量, 'failed': 失败数量}
        """
        if not items:
            return {'created': 0, 'updated': 0, 'failed': 0}

        created_count = 0
        updated_count = 0
        failed_count = 0

        # 对于已存在的数据，我们需要逐个进行upsert操作
        # 因为Directus没有原生的批量upsert支持
        self.logger.debug(f"开始批量upsert {len(items)} 条记录")

        for i, item in enumerate(items):
            try:
                pk_value = item.get(primary_key_field)
                if not pk_value:
                    failed_count += 1
                    self.logger.error(f"项目缺少主键字段 {primary_key_field}")
                    continue

                # 使用优化的upsert方法
                result, is_update = self.upsert_item(collection, str(pk_value), item)

                if is_update:
                    updated_count += 1
                else:
                    created_count += 1

                # 每处理10条记录显示一次进度
                if (i + 1) % 10 == 0:
                    self.logger.debug(f"批量upsert进度: {i + 1}/{len(items)}")

            except Exception as item_e:
                self.logger.error(f"单个项目upsert失败 {pk_value}: {str(item_e)}")
                failed_count += 1

        return {'created': created_count, 'updated': updated_count, 'failed': failed_count}
