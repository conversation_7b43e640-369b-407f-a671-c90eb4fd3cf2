# fengzs_collector.py 修复说明

## 问题描述

程序在运行时出现以下错误：
```
2025-07-30 14:03:39,073 - ERROR - Directus API请求失败: POST https://api.mendeleev.cn/items/forders - 400 - {'errors': [{'message': 'Value has to be unique.', 'extensions': {'collection': None, 'field': 'orderNo', 'value': None, 'code': 'RECORD_NOT_UNIQUE'}}]}
```

## 问题分析

1. **根本原因**: 从凤之树API获取的订单数据中，某些订单的 `orderNo` 字段为 `None` 或空值
2. **触发条件**: 当多个订单的 `orderNo` 都为 `None` 时，违反了数据库的唯一性约束
3. **影响范围**: 批量处理时，一个无效订单会导致整个批次失败

## 修复方案

### 1. 数据验证增强

**文件**: `fengzs_collector.py`

#### 1.1 在 `prepare_order_data` 方法中添加验证
```python
def prepare_order_data(self, order: Dict) -> Dict:
    """准备订单数据用于批量处理"""
    # 首先验证orderNo是否存在且不为空
    order_no = order.get('orderNo')
    if not order_no or order_no in [None, '', 'null', 'undefined']:
        raise ValueError(f"订单号为空或无效: {order_no}")
    # ... 其余代码
```

#### 1.2 添加订单数据验证方法
```python
def validate_order_data(self, order: Dict) -> bool:
    """验证订单数据是否有效"""
    order_no = order.get('orderNo')
    if not order_no or order_no in [None, '', 'null', 'undefined']:
        self.logger.warning(f"订单号无效: {order_no}")
        return False
    return True

def filter_valid_orders(self, orders: List[Dict]) -> List[Dict]:
    """过滤出有效的订单数据"""
    valid_orders = []
    invalid_count = 0
    
    for order in orders:
        if self.validate_order_data(order):
            valid_orders.append(order)
        else:
            invalid_count += 1
    
    if invalid_count > 0:
        self.logger.warning(f"过滤掉 {invalid_count} 个无效订单，保留 {len(valid_orders)} 个有效订单")
    
    return valid_orders
```

#### 1.3 在批量处理中添加预检查
```python
# 在批量处理循环中
for order in batch_orders:
    try:
        # 首先检查orderNo是否有效
        order_no = order.get('orderNo')
        if not order_no or order_no in [None, '', 'null', 'undefined']:
            failed_orders += 1
            self.logger.warning(f"跳过无效订单号的订单: {order_no}, 原始数据: {order}")
            continue
        # ... 其余处理逻辑
    except ValueError as ve:
        # 处理orderNo验证错误
        failed_orders += 1
        self.logger.warning(f"订单数据验证失败: {str(ve)}")
```

### 2. 错误处理改进

**文件**: `directus_api_client.py`

#### 2.1 增强重复键错误检测
```python
# 检查多种可能的重复键错误格式
is_duplicate_error = (
    "duplicate" in error_str or
    "unique" in error_str or
    "already exists" in error_str or
    "record_not_unique" in error_str or
    "RECORD_NOT_UNIQUE" in str(create_e) or
    "value has to be unique" in error_str  # 添加新的错误格式检查
)
```

#### 2.2 添加主键验证
```python
# 检查是否是因为主键为None导致的错误
if not primary_key or primary_key in ['None', 'null', '']:
    raise ValueError(f"主键值无效: {primary_key}")
```

### 3. 日志记录增强

#### 3.1 在获取订单数据时添加空值检测
```python
# 记录详细的订单ID，并检查空值
order_ids = []
empty_order_count = 0
for order in orders:
    order_no = order.get('orderNo', 'unknown')
    if not order_no or order_no in [None, '', 'null', 'undefined']:
        empty_order_count += 1
        order_ids.append(f"EMPTY_{empty_order_count}")
    else:
        order_ids.append(order_no)

self.logger.info(f"渠道 {channel_id} 第 {page} 页获取到订单IDs: {', '.join(order_ids)}")
if empty_order_count > 0:
    self.logger.warning(f"渠道 {channel_id} 第 {page} 页发现 {empty_order_count} 个空订单号")
```

#### 3.2 在主流程中应用过滤
```python
# 获取订单数据（凤之树 API，需要延时）
raw_orders = self.get_orders_data(channel_id)

if not raw_orders:
    self.logger.warning(f"渠道 {channel_id} 在时间区间 {begin_date} 至 {end_date} 未获取到任何订单数据")
    continue

# 过滤有效订单
orders = self.filter_valid_orders(raw_orders)
total_orders += len(orders)

if not orders:
    self.logger.warning(f"渠道 {channel_id} 在过滤后没有有效订单数据")
    continue
```

## 修复效果

1. **防止重复键错误**: 通过预先过滤无效订单，避免了 `orderNo` 为 `None` 的订单进入数据库
2. **提高程序稳定性**: 单个无效订单不会影响整个批次的处理
3. **增强日志记录**: 提供更详细的调试信息，便于问题排查
4. **优雅降级**: 遇到无效数据时记录警告并继续处理，而不是崩溃

## 测试验证

创建了 `test_fix.py` 测试脚本，验证了：
- 订单数据验证功能正常工作
- 无效订单被正确过滤
- 错误处理机制有效
- 日志记录详细准确

测试结果显示修复成功，程序能够正确处理包含无效 `orderNo` 的订单数据。

## 建议

1. **定期监控**: 关注日志中关于无效订单的警告信息
2. **数据源检查**: 如果无效订单数量过多，建议检查凤之树API的数据质量
3. **进一步优化**: 可以考虑添加更多字段的验证，如 `createTime`、`businessStatus` 等
